import React from 'react';
import { useTheme } from '../../../providers/ThemeProvider';
import { cn } from '../../../utils/cn';

export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  'data-testid'?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  type = 'button',
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  const baseClasses =
    'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const variantStyles = {
    primary: {
      backgroundColor: colors.primary,
      color: '#ffffff',
      border: `1px solid ${colors.primary}`,
      '--hover-bg': colors.primary,
      '--focus-ring': colors.primary,
    },
    secondary: {
      backgroundColor: colors.secondary,
      color: '#ffffff',
      border: `1px solid ${colors.secondary}`,
      '--hover-bg': colors.secondary,
      '--focus-ring': colors.secondary,
    },
    outline: {
      backgroundColor: 'transparent',
      color: colors.primary,
      border: `1px solid ${colors.border}`,
      '--hover-bg': colors.surface,
      '--focus-ring': colors.primary,
    },
    ghost: {
      backgroundColor: 'transparent',
      color: colors.text,
      border: '1px solid transparent',
      '--hover-bg': colors.surface,
      '--focus-ring': colors.primary,
    },
    danger: {
      backgroundColor: colors.error,
      color: '#ffffff',
      border: `1px solid ${colors.error}`,
      '--hover-bg': colors.error,
      '--focus-ring': colors.error,
    },
  };

  const buttonClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${fullWidth ? 'w-full' : ''}
    ${loading ? 'cursor-wait' : ''}
    ${className}
  `.trim();

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(event);
    }
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      style={variantStyles[variant]}
      disabled={disabled || loading}
      onClick={handleClick}
      data-testid={testId}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;
